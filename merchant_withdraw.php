<?php
/**
 * API 2: 商户发起提现
 * 所需参数: 商户秘钥, 提现金额
 * 返回: 提现ID, 商户ID, 提现金额, 原始金额, 浮动金额, 现有金额
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';
$withdrawal_amount = $_GET['withdrawal_amount'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($withdrawal_amount) || !is_numeric($withdrawal_amount) || $withdrawal_amount <= 0) {
    echo json_encode([
        'status' => 'error',
        'message' => '提现金额必须大于0',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // 验证商户并获取当前余额
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        $pdo->rollBack();
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $original_balance = $merchant['merchant_balance'];
    $withdrawal_amount = floatval($withdrawal_amount);
    
    // 检查余额是否足够
    if ($original_balance < $withdrawal_amount) {
        $pdo->rollBack();
        echo json_encode([
            'status' => 'error',
            'message' => '余额不足，当前余额: ' . $original_balance,
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 计算浮动金额（这里可以添加手续费逻辑）
    $fee_amount = 0; // 手续费，可以根据需要调整
    
    // 更新商户余额
    $new_balance = $original_balance - $withdrawal_amount;
    $stmt = $pdo->prepare("UPDATE merchants SET merchant_balance = ? WHERE merchant_secret = ?");
    $stmt->execute([$new_balance, $merchant_secret]);
    
    // 创建提现记录
    $stmt = $pdo->prepare("INSERT INTO withdrawals (merchant_id, withdrawal_amount, withdrawal_status) VALUES (?, ?, 'pending')");
    $stmt->execute([$merchant['merchant_id'], $withdrawal_amount]);
    $withdrawal_id = $pdo->lastInsertId();
    
    // 提交事务
    $pdo->commit();
    
    // 返回结果
    $result = [
        'withdrawal_id' => $withdrawal_id,
        'merchant_id' => $merchant['merchant_id'],
        'withdrawal_amount' => $withdrawal_amount,
        'original_balance' => $original_balance,
        'fee_amount' => $fee_amount,
        'current_balance' => $new_balance
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '提现申请成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '提现失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 