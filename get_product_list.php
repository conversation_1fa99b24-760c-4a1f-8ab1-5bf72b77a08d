<?php
/**
 * API 4: 商户商品列表
 * 所需参数: 商户秘钥
 * 返回: 该商户所有现有商品数据
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 查询商品列表
    $stmt = $pdo->prepare("
        SELECT 
            product_id,
            merchant_id,
            product_name,
            product_price,
            product_description,
            stock_quantity,
            status,
            created_at,
            updated_at
        FROM products 
        WHERE merchant_id = ? AND status != 'deleted'
        ORDER BY created_at DESC
    ");
    $stmt->execute([$merchant['merchant_id']]);
    $products = $stmt->fetchAll();
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取商品列表成功',
        'data' => $products
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取商品列表失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 