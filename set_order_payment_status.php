<?php
/**
 * API 12: 设置订单支付状态
 * 所需参数: 订单ID, 支付状态
 * 返回: 设置状态, 设置后的订单信息数据
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$order_id = $_GET['order_id'] ?? '';
$payment_status = $_GET['payment_status'] ?? '';

// 验证参数
if (empty($order_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '订单ID不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($payment_status)) {
    echo json_encode([
        'status' => 'error',
        'message' => '支付状态不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证支付状态值
$valid_statuses = ['unpaid', 'paid', 'delivered', 'cancelled'];
if (!in_array($payment_status, $valid_statuses)) {
    echo json_encode([
        'status' => 'error',
        'message' => '支付状态值无效，有效值: ' . implode(', ', $valid_statuses),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询订单是否存在
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode([
            'status' => 'error',
            'message' => '订单不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 更新订单状态
    $stmt = $pdo->prepare("UPDATE orders SET order_status = ? WHERE order_id = ?");
    $stmt->execute([$payment_status, $order_id]);
    
    // 如果订单状态变为已支付，自动分配卡密
    if ($payment_status === 'paid' && $order['order_status'] === 'unpaid') {
        // 查找可用的卡密
        $stmt = $pdo->prepare("
            SELECT card_key_id, card_key_content 
            FROM card_keys 
            WHERE product_name = ? AND card_key_status = 'unsold' 
            LIMIT 1
        ");
        $stmt->execute([$order['product_name']]);
        $card_key = $stmt->fetch();
        
        if ($card_key) {
            // 更新卡密状态为已售出
            $stmt = $pdo->prepare("
                UPDATE card_keys 
                SET card_key_status = 'sold', order_id = ? 
                WHERE card_key_id = ?
            ");
            $stmt->execute([$order_id, $card_key['card_key_id']]);
            
            // 更新订单的发货内容
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET delivery_content = ? 
                WHERE order_id = ?
            ");
            $stmt->execute([$card_key['card_key_content'], $order_id]);
            
            // 更新商品库存
            $stmt = $pdo->prepare("
                UPDATE products SET stock_quantity = (
                    SELECT COUNT(*) FROM card_keys 
                    WHERE product_name = ? AND card_key_status = 'unsold'
                ) WHERE product_name = ?
            ");
            $stmt->execute([$order['product_name'], $order['product_name']]);
        }
    }
    
    // 提交事务
    $pdo->commit();
    
    // 获取更新后的订单信息
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $updated_order = $stmt->fetch();
    
    $result = [
        'order_id' => $order_id,
        'old_status' => $order['order_status'],
        'new_status' => $payment_status,
        'order_info' => $updated_order
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '订单状态更新成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '设置订单支付状态失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 