<?php
/**
 * API 6: 商户订单列表
 * 所需参数: 商户秘钥
 * 返回: 该商户所有的订单数据
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 查询订单列表
    $stmt = $pdo->prepare("
        SELECT 
            order_id,
            merchant_id,
            product_name,
            product_price,
            purchase_time,
            delivery_content,
            order_status,
            customer_contact,
            created_at,
            updated_at
        FROM orders 
        WHERE merchant_id = ?
        ORDER BY purchase_time DESC
    ");
    $stmt->execute([$merchant['merchant_id']]);
    $orders = $stmt->fetchAll();
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取订单列表成功',
        'data' => $orders
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取订单列表失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 