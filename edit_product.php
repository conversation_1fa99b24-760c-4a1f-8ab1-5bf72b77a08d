<?php
/**
 * API 9: 编辑商品信息
 * 所需参数: 商户秘钥, 商品ID, 需修改的参数(商品名称, 商品价格, 商品简介) 不传递则代表不修改
 * 返回: 编辑后的该商品信息
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';
$product_id = $_GET['product_id'] ?? '';
$product_name = $_GET['product_name'] ?? '';
$product_price = $_GET['product_price'] ?? '';
$product_description = $_GET['product_description'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证商品是否属于该商户
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ? AND merchant_id = ? AND status != 'deleted'");
    $stmt->execute([$product_id, $merchant['merchant_id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品不存在或无权限操作',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 构建更新字段
    $update_fields = [];
    $update_values = [];
    
    if (!empty($product_name)) {
        $update_fields[] = 'product_name = ?';
        $update_values[] = $product_name;
    }
    
    if (!empty($product_price)) {
        if (!is_numeric($product_price) || $product_price <= 0) {
            echo json_encode([
                'status' => 'error',
                'message' => '商品价格必须大于0',
                'data' => null
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
        $update_fields[] = 'product_price = ?';
        $update_values[] = floatval($product_price);
    }
    
    if (isset($_GET['product_description'])) { // 允许空值
        $update_fields[] = 'product_description = ?';
        $update_values[] = $product_description;
    }
    
    if (empty($update_fields)) {
        echo json_encode([
            'status' => 'error',
            'message' => '没有需要更新的字段',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 添加商品ID到更新值数组
    $update_values[] = $product_id;
    
    // 执行更新
    $sql = "UPDATE products SET " . implode(', ', $update_fields) . " WHERE product_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($update_values);
    
    // 获取更新后的商品信息
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $updated_product = $stmt->fetch();
    
    echo json_encode([
        'status' => 'success',
        'message' => '商品信息更新成功',
        'data' => $updated_product
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '编辑商品信息失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 