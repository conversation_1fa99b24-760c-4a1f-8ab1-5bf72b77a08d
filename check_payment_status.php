<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$order_id = $_GET['order_id'] ?? '';

if (empty($order_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '订单ID不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询订单信息
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode([
            'status' => 'error',
            'message' => '订单不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果订单已经是已支付状态，直接返回
    if ($order['order_status'] === 'paid') {
        echo json_encode([
            'status' => 'success',
            'message' => '订单已支付',
            'data' => $order
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 调用第三方支付接口检查支付状态
    $payment_status = checkThirdPartyPayment($order_id);
    
    if ($payment_status) {
        // 支付成功，更新订单状态
        $pdo->beginTransaction();
        
        // 查找可用的卡密
        $stmt = $pdo->prepare("SELECT card_key_id, card_key_content FROM card_keys WHERE product_name = ? AND card_key_status = 'unsold' LIMIT 1");
        $stmt->execute([$order['product_name']]);
        $card_key = $stmt->fetch();
        
        if ($card_key) {
            // 更新卡密状态为已售出
            $stmt = $pdo->prepare("UPDATE card_keys SET card_key_status = 'sold', order_id = ? WHERE card_key_id = ?");
            $stmt->execute([$order_id, $card_key['card_key_id']]);
            
            // 更新订单状态和发货内容
            $stmt = $pdo->prepare("UPDATE orders SET order_status = 'paid', delivery_content = ? WHERE order_id = ?");
            $stmt->execute([$card_key['card_key_content'], $order_id]);
            
            // 更新商品库存
            $stmt = $pdo->prepare("UPDATE products SET stock_quantity = (SELECT COUNT(*) FROM card_keys WHERE product_name = ? AND card_key_status = 'unsold') WHERE product_name = ?");
            $stmt->execute([$order['product_name'], $order['product_name']]);
            
            // 更新商户余额
            $stmt = $pdo->prepare("UPDATE merchants SET merchant_balance = merchant_balance + ? WHERE merchant_id = ?");
            $stmt->execute([$order['product_price'], $order['merchant_id']]);
        } else {
            // 没有可用卡密，只更新订单状态
            $stmt = $pdo->prepare("UPDATE orders SET order_status = 'paid' WHERE order_id = ?");
            $stmt->execute([$order_id]);
        }
        
        $pdo->commit();
        
        // 获取更新后的订单信息
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $updated_order = $stmt->fetch();
        
        echo json_encode([
            'status' => 'success',
            'message' => '支付成功，订单已更新',
            'data' => $updated_order
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // 支付未完成
        echo json_encode([
            'status' => 'success',
            'message' => '订单未支付',
            'data' => $order
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '检查支付状态失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 检查第三方支付状态函数
function checkThirdPartyPayment($order_id) {
    // 支付网关配置
    $domain = "https://aftpay.njdzn.com/";
    $pid = "2222";
    $key = "4j4x1jI4Uj1UZX1A2QqACCF1UAqA4AAA";

    $params = [
        'act' => 'order',
        'pid' => $pid,
        'key' => $key,
        'out_trade_no' => $order_id
    ];
    $check_url = $domain . "api.php?" . http_build_query($params);

    $log_dir = __DIR__ . '/log';
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0777, true);
    }
    $log_file = $log_dir . '/' . date('Y-m-d') . '.log';
    $log_time = date('Y-m-d H:i:s');

    try {
        $opts = [
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36\r\n"
            ]
        ];
        $context = stream_context_create($opts);
        $response = file_get_contents($check_url, false, $context);
        $log_content = "[$log_time] 查询订单号: $order_id\nGET URL: $check_url\n返回: $response\n";
        file_put_contents($log_file, $log_content, FILE_APPEND);
        if ($response !== false) {
            $data = json_decode($response, true);
            if ($data && isset($data['status'])) {
                return $data['status'] == 1; // 1为支付成功
            }
        }
    } catch (Exception $e) {
        $err = "[$log_time] 查询异常: " . $e->getMessage() . "\n";
        file_put_contents($log_file, $err, FILE_APPEND);
    }
    return false;
}
?> 