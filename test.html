<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多商户商品管理系统 - API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .api-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .api-header h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .api-header .description {
            color: #666;
            font-size: 0.9em;
        }
        
        .api-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: white;
            border-bottom-color: #667eea;
            color: #667eea;
        }
        
        .tab:hover {
            background: #e9ecef;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .quick-test {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .quick-test h3 {
            color: #2d5a2d;
            margin-bottom: 15px;
        }
        
        .quick-test .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>多商户商品管理系统</h1>
            <p>API接口测试页面</p>
        </div>
        
        <div class="content">
            <!-- 快速测试区域 -->
            <div class="quick-test">
                <h3>🚀 快速测试</h3>
                <button class="btn" onclick="testDatabaseConnection()">测试数据库连接</button>
                <button class="btn" onclick="importDatabase()">导入数据库</button>
                <button class="btn" onclick="testAllAPIs()">测试所有API</button>
                <div id="quick-result" class="result" style="display: none;"></div>
            </div>
            
            <!-- 标签页 -->
            <div class="tabs">
                <div class="tab active" onclick="switchTab('merchant')">商户管理</div>
                <div class="tab" onclick="switchTab('product')">商品管理</div>
                <div class="tab" onclick="switchTab('order')">订单管理</div>
                <div class="tab" onclick="switchTab('withdrawal')">提现管理</div>
                <div class="tab" onclick="switchTab('payment')">支付管理</div>
            </div>
            
            <!-- 商户管理标签页 -->
            <div id="merchant" class="tab-content active">
                <!-- 注册商户 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>1. 注册商户</h3>
                        <div class="description">根据商户ID自动生成商户信息并注册</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户ID:</label>
                            <input type="text" id="register_merchant_id" value="M003" placeholder="请输入商户ID">
                        </div>
                        <button class="btn" onclick="registerMerchant()">注册商户</button>
                        <div id="register-merchant-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 获取商户信息 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>2. 获取商户信息</h3>
                        <div class="description">根据商户ID获取商户详细信息</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户ID:</label>
                            <input type="text" id="merchant_id" value="M001" placeholder="请输入商户ID">
                        </div>
                        <button class="btn" onclick="getMerchantInfo()">获取商户信息</button>
                        <div id="merchant-info-result" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 商品管理标签页 -->
            <div id="product" class="tab-content">
                <!-- 获取商品列表 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>2. 获取商品列表</h3>
                        <div class="description">获取指定商户的所有商品</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="product_merchant_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <button class="btn" onclick="getProductList()">获取商品列表</button>
                        <div id="product-list-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 商品信息查询 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>3. 商品信息查询</h3>
                        <div class="description">根据商品ID获取商品详细信息</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="product_info_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <button class="btn" onclick="getProductInfo()">查询商品信息</button>
                        <div id="product-info-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 新增商品 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>4. 新增商品</h3>
                        <div class="description">为商户添加新商品</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="add_product_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <div class="form-group">
                            <label>商品名称:</label>
                            <input type="text" id="add_product_name" value="测试商品" placeholder="请输入商品名称">
                        </div>
                        <div class="form-group">
                            <label>商品价格:</label>
                            <input type="number" id="add_product_price" value="99.00" step="0.01" placeholder="请输入商品价格">
                        </div>
                        <div class="form-group">
                            <label>商品简介:</label>
                            <textarea id="add_product_description" placeholder="请输入商品简介">这是一个测试商品</textarea>
                        </div>
                        <button class="btn" onclick="addProduct()">新增商品</button>
                        <div id="add-product-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 编辑商品 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>5. 编辑商品</h3>
                        <div class="description">编辑商品信息</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="edit_product_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="edit_product_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <div class="form-group">
                            <label>新商品名称:</label>
                            <input type="text" id="edit_product_name" value="修改后的商品名称" placeholder="请输入新商品名称">
                        </div>
                        <div class="form-group">
                            <label>新商品价格:</label>
                            <input type="number" id="edit_product_price" value="88.00" step="0.01" placeholder="请输入新商品价格">
                        </div>
                        <button class="btn" onclick="editProduct()">编辑商品</button>
                        <div id="edit-product-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 删除商品 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>6. 删除商品</h3>
                        <div class="description">删除指定商品（软删除）</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="delete_product_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="delete_product_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <button class="btn" onclick="deleteProduct()">删除商品</button>
                        <div id="delete-product-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 获取卡密列表 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>7. 获取卡密列表</h3>
                        <div class="description">获取指定商品的所有卡密</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="card_key_product_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <button class="btn" onclick="getCardKeyList()">获取卡密列表</button>
                        <div id="card-key-list-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 添加卡密 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>8. 添加卡密</h3>
                        <div class="description">为商品添加卡密（多个卡密用-分割）</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="add_card_key_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="add_card_key_product_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <div class="form-group">
                            <label>卡密内容:</label>
                            <textarea id="add_card_key_content" placeholder="请输入卡密内容，多个卡密用-分割">CARD001-XXXX-YYYY-ZZZZ-CARD002-XXXX-YYYY-ZZZZ</textarea>
                        </div>
                        <button class="btn" onclick="addCardKeys()">添加卡密</button>
                        <div id="add-card-key-result" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 订单管理标签页 -->
            <div id="order" class="tab-content">
                <!-- 获取订单列表 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>9. 获取订单列表</h3>
                        <div class="description">获取指定商户的所有订单</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="order_merchant_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <button class="btn" onclick="getOrderList()">获取订单列表</button>
                        <div id="order-list-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 获取订单详情 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>10. 获取订单详情</h3>
                        <div class="description">获取指定订单的详细信息</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>订单ID:</label>
                            <input type="text" id="order_detail_id" value="ORDER001" placeholder="请输入订单ID">
                        </div>
                        <button class="btn" onclick="getOrderDetail()">获取订单详情</button>
                        <div id="order-detail-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 设置订单支付状态 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>11. 设置订单支付状态</h3>
                        <div class="description">设置订单的支付状态</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>订单ID:</label>
                            <input type="text" id="payment_order_id" value="ORDER001" placeholder="请输入订单ID">
                        </div>
                        <div class="form-group">
                            <label>支付状态:</label>
                            <select id="payment_status">
                                <option value="unpaid">未支付</option>
                                <option value="paid">已支付</option>
                                <option value="delivered">已发货</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <button class="btn" onclick="setPaymentStatus()">设置支付状态</button>
                        <div id="payment-status-result" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 提现管理标签页 -->
            <div id="withdrawal" class="tab-content">
                <!-- 发起提现 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>12. 发起提现</h3>
                        <div class="description">商户发起提现申请</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="withdraw_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <div class="form-group">
                            <label>提现金额:</label>
                            <input type="number" id="withdraw_amount" value="100.00" step="0.01" placeholder="请输入提现金额">
                        </div>
                        <button class="btn" onclick="merchantWithdraw()">发起提现</button>
                        <div id="withdraw-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 获取提现列表 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>13. 获取提现列表</h3>
                        <div class="description">获取指定商户的提现记录</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>商户秘钥:</label>
                            <input type="text" id="withdrawal_list_secret" value="secret_key_001" placeholder="请输入商户秘钥">
                        </div>
                        <button class="btn" onclick="getWithdrawalList()">获取提现列表</button>
                        <div id="withdrawal-list-result" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 支付管理标签页 -->
            <div id="payment" class="tab-content">
                <!-- 发起订单 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>14. 发起订单</h3>
                        <div class="description">创建订单并生成支付链接</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>客户联系方式:</label>
                            <input type="text" id="customer_contact" value="<EMAIL>" placeholder="请输入客户联系方式">
                        </div>
                        <div class="form-group">
                            <label>商品ID:</label>
                            <input type="number" id="create_order_product_id" value="1" placeholder="请输入商品ID">
                        </div>
                        <button class="btn" onclick="createOrder()">发起订单</button>
                        <div id="create-order-result" class="result" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- 检查支付状态 -->
                <div class="api-section">
                    <div class="api-header">
                        <h3>15. 检查支付状态</h3>
                        <div class="description">检查订单支付状态并同步数据库</div>
                    </div>
                    <div class="api-body">
                        <div class="form-group">
                            <label>订单ID:</label>
                            <input type="text" id="check_payment_order_id" value="ORDER001" placeholder="请输入订单ID">
                        </div>
                        <button class="btn" onclick="checkPaymentStatus()">检查支付状态</button>
                        <div id="check-payment-result" class="result" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        // 显示结果
        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }
        
        // 隐藏结果
        function hideResult(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        // 构建URL参数
        function buildParams(params) {
            return Object.keys(params)
                .filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined)
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
                .join('&');
        }
        
        // 发送API请求
        async function callAPI(apiFile, params) {
            try {
                const url = `${apiFile}?${buildParams(params)}`;
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                return {
                    status: 'error',
                    message: '请求失败: ' + error.message,
                    data: null
                };
            }
        }
        
        // 测试数据库连接
        async function testDatabaseConnection() {
            const result = await callAPI('config.php', {});
            showResult('quick-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // 导入数据库
        async function importDatabase() {
            const result = await callAPI('import_sql.php', {});
            showResult('quick-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // 测试所有API
        async function testAllAPIs() {
            showResult('quick-result', '开始测试所有API...', 'info');
            
            const tests = [
                { name: '获取商户信息', func: () => callAPI('get_merchant_info.php', { merchant_id: 'M001' }) },
                { name: '获取商品列表', func: () => callAPI('get_product_list.php', { merchant_secret: 'secret_key_001' }) },
                { name: '商品信息查询', func: () => callAPI('get_product_info.php', { product_id: '1' }) },
                { name: '获取卡密列表', func: () => callAPI('get_card_key_list.php', { product_id: '1' }) },
                { name: '获取订单列表', func: () => callAPI('get_order_list.php', { merchant_secret: 'secret_key_001' }) },
                { name: '获取提现列表', func: () => callAPI('get_withdrawal_list.php', { merchant_secret: 'secret_key_001' }) }
            ];
            
            let results = [];
            for (const test of tests) {
                const result = await test.func();
                results.push({
                    name: test.name,
                    status: result.status,
                    message: result.message
                });
            }
            
            showResult('quick-result', results, 'info');
        }
        
        // API 1: 注册商户
        async function registerMerchant() {
            const merchantId = document.getElementById('register_merchant_id').value;
            const result = await callAPI('register_merchant.php', { merchant_id: merchantId });
            showResult('register-merchant-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 2: 获取商户信息
        async function getMerchantInfo() {
            const merchantId = document.getElementById('merchant_id').value;
            const result = await callAPI('get_merchant_info.php', { merchant_id: merchantId });
            showResult('merchant-info-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 3: 获取商品列表
        async function getProductList() {
            const merchantSecret = document.getElementById('product_merchant_secret').value;
            const result = await callAPI('get_product_list.php', { merchant_secret: merchantSecret });
            showResult('product-list-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 4: 商品信息查询
        async function getProductInfo() {
            const productId = document.getElementById('product_info_id').value;
            const result = await callAPI('get_product_info.php', { product_id: productId });
            showResult('product-info-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 5: 新增商品
        async function addProduct() {
            const params = {
                merchant_secret: document.getElementById('add_product_secret').value,
                product_name: document.getElementById('add_product_name').value,
                product_price: document.getElementById('add_product_price').value,
                product_description: document.getElementById('add_product_description').value
            };
            const result = await callAPI('add_product.php', params);
            showResult('add-product-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 6: 编辑商品
        async function editProduct() {
            const params = {
                merchant_secret: document.getElementById('edit_product_secret').value,
                product_id: document.getElementById('edit_product_id').value,
                product_name: document.getElementById('edit_product_name').value,
                product_price: document.getElementById('edit_product_price').value
            };
            const result = await callAPI('edit_product.php', params);
            showResult('edit-product-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 7: 删除商品
        async function deleteProduct() {
            const params = {
                merchant_secret: document.getElementById('delete_product_secret').value,
                product_id: document.getElementById('delete_product_id').value
            };
            const result = await callAPI('delete_product.php', params);
            showResult('delete-product-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 8: 获取卡密列表
        async function getCardKeyList() {
            const productId = document.getElementById('card_key_product_id').value;
            const result = await callAPI('get_card_key_list.php', { product_id: productId });
            showResult('card-key-list-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 9: 添加卡密
        async function addCardKeys() {
            const params = {
                merchant_secret: document.getElementById('add_card_key_secret').value,
                product_id: document.getElementById('add_card_key_product_id').value,
                card_key_content: document.getElementById('add_card_key_content').value
            };
            const result = await callAPI('add_card_keys.php', params);
            showResult('add-card-key-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 10: 获取订单列表
        async function getOrderList() {
            const merchantSecret = document.getElementById('order_merchant_secret').value;
            const result = await callAPI('get_order_list.php', { merchant_secret: merchantSecret });
            showResult('order-list-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 11: 获取订单详情
        async function getOrderDetail() {
            const orderId = document.getElementById('order_detail_id').value;
            const result = await callAPI('get_order_detail.php', { order_id: orderId });
            showResult('order-detail-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 12: 设置订单支付状态
        async function setPaymentStatus() {
            const params = {
                order_id: document.getElementById('payment_order_id').value,
                payment_status: document.getElementById('payment_status').value
            };
            const result = await callAPI('set_order_payment_status.php', params);
            showResult('payment-status-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 13: 发起提现
        async function merchantWithdraw() {
            const params = {
                merchant_secret: document.getElementById('withdraw_secret').value,
                withdrawal_amount: document.getElementById('withdraw_amount').value
            };
            const result = await callAPI('merchant_withdraw.php', params);
            showResult('withdraw-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 14: 获取提现列表
        async function getWithdrawalList() {
            const merchantSecret = document.getElementById('withdrawal_list_secret').value;
            const result = await callAPI('get_withdrawal_list.php', { merchant_secret: merchantSecret });
            showResult('withdrawal-list-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 15: 发起订单
        async function createOrder() {
            const params = {
                customer_contact: document.getElementById('customer_contact').value,
                product_id: document.getElementById('create_order_product_id').value
            };
            const result = await callAPI('create_order.php', params);
            showResult('create-order-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // API 16: 检查支付状态
        async function checkPaymentStatus() {
            const orderId = document.getElementById('check_payment_order_id').value;
            const result = await callAPI('check_payment_status.php', { order_id: orderId });
            showResult('check-payment-result', result, result.status === 'success' ? 'success' : 'error');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('多商户商品管理系统测试页面已加载');
        });
    </script>
</body>
</html> 