<?php
/**
 * API 11: 获取订单内容
 * 所需参数: 订单ID
 * 返回: 该商品的所有信息,若未支付, 则发货内容用******代替输出
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$order_id = $_GET['order_id'] ?? '';

// 验证参数
if (empty($order_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '订单ID不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 查询订单详情
    $stmt = $pdo->prepare("
        SELECT 
            order_id,
            merchant_id,
            product_name,
            product_price,
            purchase_time,
            delivery_content,
            order_status,
            customer_contact,
            created_at,
            updated_at
        FROM orders 
        WHERE order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo json_encode([
            'status' => 'error',
            'message' => '订单不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 如果订单未支付，隐藏发货内容
    if ($order['order_status'] === 'unpaid') {
        $order['delivery_content'] = '******';
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取订单详情成功',
        'data' => $order
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取订单详情失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 