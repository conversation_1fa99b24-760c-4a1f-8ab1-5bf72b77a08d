<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$withdrawal_id = $_GET['withdrawal_id'] ?? '';
$admin_password = $_GET['admin_password'] ?? '';
$status = $_GET['status'] ?? '';

// 终极管理员密码
$SUPER_ADMIN_PASSWORD = "yjsyjs_admin_2024";

if (empty($withdrawal_id) || empty($admin_password) || empty($status)) {
    echo json_encode([
        'status' => 'error',
        'message' => '提现ID、管理员密码和状态不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证管理员密码
if ($admin_password !== $SUPER_ADMIN_PASSWORD) {
    echo json_encode([
        'status' => 'error',
        'message' => '管理员密码错误',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 验证状态值
$allowed_statuses = ['pending', 'approved', 'rejected', 'completed'];
if (!in_array($status, $allowed_statuses)) {
    echo json_encode([
        'status' => 'error',
        'message' => '无效的状态值，允许的状态：' . implode(', ', $allowed_statuses),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 检查提现记录是否存在
    $stmt = $pdo->prepare("SELECT * FROM withdrawals WHERE withdrawal_id = ?");
    $stmt->execute([$withdrawal_id]);
    $withdrawal = $stmt->fetch();
    
    if (!$withdrawal) {
        echo json_encode([
            'status' => 'error',
            'message' => '提现记录不存在',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    $old_status = $withdrawal['withdrawal_status'];
    
    // 更新提现状态
    $stmt = $pdo->prepare("UPDATE withdrawals SET withdrawal_status = ?, updated_at = NOW() WHERE withdrawal_id = ?");
    $stmt->execute([$status, $withdrawal_id]);
    
    // 如果状态变为rejected，需要退还余额给商户
    if ($status === 'rejected' && $old_status !== 'rejected') {
        $stmt = $pdo->prepare("UPDATE merchants SET merchant_balance = merchant_balance + ?, updated_at = NOW() WHERE merchant_id = ?");
        $stmt->execute([$withdrawal['withdrawal_amount'], $withdrawal['merchant_id']]);
    }
    
    // 获取更新后的提现记录
    $stmt = $pdo->prepare("
        SELECT w.*, m.payment_qr_code, m.shop_name 
        FROM withdrawals w 
        LEFT JOIN merchants m ON w.merchant_id = m.merchant_id 
        WHERE w.withdrawal_id = ?
    ");
    $stmt->execute([$withdrawal_id]);
    $updated_withdrawal = $stmt->fetch();
    
    $result = [
        'withdrawal_id' => $updated_withdrawal['withdrawal_id'],
        'merchant_id' => $updated_withdrawal['merchant_id'],
        'shop_name' => $updated_withdrawal['shop_name'],
        'withdrawal_amount' => $updated_withdrawal['withdrawal_amount'],
        'old_status' => $old_status,
        'new_status' => $status,
        'payment_qr_code' => $updated_withdrawal['payment_qr_code'],
        'created_at' => $updated_withdrawal['created_at'],
        'updated_at' => $updated_withdrawal['updated_at']
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => '提现状态更新成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '更新提现状态失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 