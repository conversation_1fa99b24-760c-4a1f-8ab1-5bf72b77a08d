<?php
/**
 * API 3: 商户提现列表
 * 所需参数: 商户秘钥
 * 返回: 该商户的所有的提现信息数据
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 根据商户秘钥查找商户ID
    $stmt = $pdo->prepare("SELECT merchant_id FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户不存在或秘钥错误',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 获取提现列表，包含收款码信息
    $stmt = $pdo->prepare("
        SELECT w.*, m.payment_qr_code 
        FROM withdrawals w 
        LEFT JOIN merchants m ON w.merchant_id = m.merchant_id 
        WHERE w.merchant_id = ? 
        ORDER BY w.created_at DESC
    ");
    $stmt->execute([$merchant['merchant_id']]);
    $withdrawals = $stmt->fetchAll();
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取提现列表成功',
        'data' => $withdrawals
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取提现列表失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 