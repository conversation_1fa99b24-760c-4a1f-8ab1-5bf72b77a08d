<?php
header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$admin_password = $_GET['admin_password'] ?? '';

// 终极管理员密码
$SUPER_ADMIN_PASSWORD = "yjsyjs_admin_2024";

if (empty($admin_password) || $admin_password !== $SUPER_ADMIN_PASSWORD) {
    echo json_encode([
        'status' => 'error',
        'message' => '管理员密码错误或未提供',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 获取所有提现记录，包含商户信息和收款码
    $stmt = $pdo->prepare("
        SELECT w.*, m.shop_name, m.payment_qr_code 
        FROM withdrawals w 
        LEFT JOIN merchants m ON w.merchant_id = m.merchant_id 
        ORDER BY w.created_at DESC
    ");
    $stmt->execute();
    $withdrawals = $stmt->fetchAll();
    
    echo json_encode([
        'status' => 'success',
        'message' => '获取提现列表成功',
        'data' => $withdrawals
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '获取提现列表失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 