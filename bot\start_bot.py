#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Telegram机器人启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import main

if __name__ == '__main__':
    print("🚀 启动Telegram机器人...")
    print("📡 API地址: http://127.0.0.1:4562/")
    print("🤖 机器人Token: 8013186653:AAHKx4XIDvi7rCyJMWBINmTQBD_Yr_ZSEhg")
    print("=" * 50)
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 机器人已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1) 