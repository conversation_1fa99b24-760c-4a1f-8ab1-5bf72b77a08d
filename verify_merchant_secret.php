<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');

$merchant_id = $_GET['merchant_id'] ?? '';
$merchant_secret = $_GET['merchant_secret'] ?? '';

if (empty($merchant_id) || empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户ID和商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 根据商户ID生成商户秘钥（可逆算法）
function generateMerchantSecret($merchant_id) {
    // 使用固定的密钥和算法
    $key = "yjsyjs_merchant_secret_key_2024";
    $salt = "yjsyjs_salt_2024";
    
    // 组合商户ID、密钥和盐值
    $combined = $merchant_id . $key . $salt;
    
    // 使用MD5生成32位字符串
    $hash = md5($combined);
    
    // 添加商户ID前缀，确保唯一性
    $prefix = substr($merchant_id, 0, 3);
    
    // 组合成最终的商户秘钥
    $merchant_secret = $prefix . "_" . $hash;
    
    return $merchant_secret;
}

// 验证商户秘钥
$expected_secret = generateMerchantSecret($merchant_id);
$is_valid = $merchant_secret === $expected_secret;

$result = [
    'merchant_id' => $merchant_id,
    'provided_secret' => $merchant_secret,
    'expected_secret' => $expected_secret,
    'is_valid' => $is_valid
];

echo json_encode([
    'status' => $is_valid ? 'success' : 'error',
    'message' => $is_valid ? '商户秘钥验证成功' : '商户秘钥验证失败',
    'data' => $result
], JSON_UNESCAPED_UNICODE);
?> 