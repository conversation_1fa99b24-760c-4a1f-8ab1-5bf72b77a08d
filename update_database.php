<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    echo json_encode([
        'status' => 'error',
        'message' => '数据库连接失败',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 开始事务
    $pdo->beginTransaction();
    
    // 1. 修改withdrawal_status字段的enum定义
    $sql1 = "ALTER TABLE `withdrawals` MODIFY COLUMN `withdrawal_status` enum('pending','approved','rejected','completed','failed') DEFAULT 'pending' COMMENT '提现状态'";
    $pdo->exec($sql1);
    
    // 2. 检查updated_at字段是否存在，如果不存在则添加
    $stmt = $pdo->query("SHOW COLUMNS FROM `withdrawals` LIKE 'updated_at'");
    if ($stmt->rowCount() == 0) {
        $sql2 = "ALTER TABLE `withdrawals` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `created_at`";
        $pdo->exec($sql2);
        
        // 3. 更新现有记录的updated_at字段
        $sql3 = "UPDATE `withdrawals` SET `updated_at` = `created_at` WHERE `updated_at` IS NULL";
        $pdo->exec($sql3);
    }
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode([
        'status' => 'success',
        'message' => '数据库字段更新成功',
        'data' => [
            'updated_fields' => [
                'withdrawal_status' => 'enum(\'pending\',\'approved\',\'rejected\',\'completed\',\'failed\')',
                'updated_at' => 'timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            ]
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '数据库更新失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 