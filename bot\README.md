# Telegram 多商户商品管理机器人

## 功能概述

这是一个基于Python的Telegram机器人，用于多商户商品管理系统。用户可以通过机器人浏览商户商品、下单购买和支付。

## 主要功能

### 1. 商户访问
- 使用 `/start 商户ID` 命令访问指定商户
- 例如：`/start M001`

### 2. 商品浏览
- 显示商户信息和商品列表
- 点击商品查看详细信息
- 支持返回商品列表

### 3. 订单管理
- 创建订单并生成支付链接
- 检查支付状态
- 支付成功后显示发货内容

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动机器人
```bash
python start_bot.py
```

或者直接运行：
```bash
python main.py
```

## 使用流程

1. **访问商户**
   - 发送 `/start M001` 给机器人
   - 机器人会显示商户信息和商品列表

2. **浏览商品**
   - 点击商品名称查看详情
   - 查看商品价格、库存等信息

3. **购买商品**
   - 点击"购买"按钮创建订单
   - 系统生成支付链接和订单信息

4. **支付订单**
   - 点击"立即支付"跳转到支付页面
   - 或点击"我已支付"检查支付状态

5. **获取商品**
   - 支付成功后显示发货内容
   - 未支付时弹出提示，按钮保持不变

## 技术特点

- **美观界面**：使用HTML格式和emoji表情
- **内联按钮**：提供良好的用户交互体验
- **状态管理**：实时检查订单支付状态
- **错误处理**：完善的错误提示和处理机制

## 配置说明

- **机器人Token**：`8013186653:AAHKx4XIDvi7rCyJMWBINmTQBD_Yr_ZSEhg`
- **API地址**：`http://127.0.0.1:4562/`
- **商户秘钥算法**：与PHP版本保持一致

## 注意事项

1. 确保PHP API服务正在运行
2. 确保网络连接正常
3. 商户ID必须存在于系统中
4. 支付状态检查需要第三方支付接口支持

## 文件结构

```
bot/
├── main.py              # 主程序文件
├── start_bot.py         # 启动脚本
├── requirements.txt     # Python依赖
└── README.md           # 说明文档
``` 