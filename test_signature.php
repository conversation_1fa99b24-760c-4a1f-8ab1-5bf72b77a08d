<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>签名算法测试（按照pay.py逻辑）</h1>";

// 支付网关配置
$domain = "http://aczhifutong.ndvfp.cn";
$pid = "10002";
$key = "yYiQiENZy2luHdhZlEqwq9VQY9U5hd99";

$params = [
    "pid" => $pid,
    "type" => "wxpay",
    "out_trade_no" => "ORDER123456789",
    "notify_url" => $domain . "/notify",
    "return_url" => $domain . "/return",
    "name" => "测试商品",
    "money" => "99.00",
    "sitename" => "星云商店",
    "sign_type" => "MD5"
];

echo "<h2>原始参数:</h2>";
echo "<pre>" . print_r($params, true) . "</pre>";

// 按照pay.py的签名算法
// 1. 筛选参数（排除空值和以sign开头的参数）
$para_filter = [];
foreach ($params as $k => $v) {
    if ($v != "" && strpos($k, "sign") !== 0) {
        $para_filter[$k] = $v;
    }
}

echo "<h2>筛选后的参数:</h2>";
echo "<pre>" . print_r($para_filter, true) . "</pre>";

// 2. 排序（按ASCII码递增排序）
ksort($para_filter);

echo "<h2>排序后的参数:</h2>";
echo "<pre>" . print_r($para_filter, true) . "</pre>";

// 3. 拼接参数（格式：key=value&key=value）
$prestr_parts = [];
foreach ($para_filter as $k => $v) {
    $prestr_parts[] = $k . "=" . $v;
}
$prestr = implode("&", $prestr_parts);

echo "<h2>待签名字符串:</h2>";
echo "<pre>" . $prestr . "</pre>";

// 4. MD5加密（prestr + key）
$sign = md5($prestr . $key);

echo "<h2>签名结果:</h2>";
echo "<pre>MD5(" . $prestr . $key . ") = " . $sign . "</pre>";

$params["sign"] = $sign;

echo "<h2>最终参数:</h2>";
echo "<pre>" . print_r($params, true) . "</pre>";

$final_url = $domain . "/submit.php?" . http_build_query($params);

echo "<h2>最终URL:</h2>";
echo "<pre>" . $final_url . "</pre>";

// 对比Python版本的逻辑
echo "<h2>Python版本逻辑对比:</h2>";
echo "<pre>";
echo "Python: para_filter = {k: v for k, v in params.items() if v != \"\" and not k.startswith(\"sign\")}\n";
echo "Python: para_sort = dict(sorted(para_filter.items()))\n";
echo "Python: prestr = \"&\".join([f\"{k}={v}\" for k, v in para_sort.items()])\n";
echo "Python: sign = hashlib.md5((prestr + KEY).encode('utf-8')).hexdigest()\n";
echo "</pre>";
?> 