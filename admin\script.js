// 商户管理后台公共JavaScript

// 显示消息提示
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = message;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 显示加载状态
function showLoading(element) {
    element.innerHTML = '<div class="loading">加载中...</div>';
}

// 隐藏加载状态
function hideLoading(element, content) {
    element.innerHTML = content;
}

// 格式化金额
function formatMoney(amount) {
    return parseFloat(amount).toFixed(2);
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 获取URL参数
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 设置当前活动标签
function setActiveTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 设置当前标签为活动状态
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
}

// 切换标签页内容
function switchTab(tabName) {
    // 隐藏所有内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // 显示当前标签内容
    const activeContent = document.getElementById(tabName);
    if (activeContent) {
        activeContent.style.display = 'block';
    }
    
    // 设置活动标签
    setActiveTab(tabName);
}

// 文件上传处理
function handleFileUpload(input, previewElement, callback) {
    const file = input.files[0];
    if (!file) return;
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        showAlert('请选择图片文件', 'error');
        return;
    }
    
    // 检查文件大小 (最大5MB)
    if (file.size > 5 * 1024 * 1024) {
        showAlert('文件大小不能超过5MB', 'error');
        return;
    }
    
    // 预览图片
    const reader = new FileReader();
    reader.onload = function(e) {
        if (previewElement) {
            previewElement.src = e.target.result;
            previewElement.style.display = 'block';
        }
        
        if (callback) {
            callback(file);
        }
    };
    reader.readAsDataURL(file);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// AJAX请求封装
function apiRequest(url, params = {}, method = 'GET') {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        if (method === 'GET') {
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = queryString ? `${url}?${queryString}` : url;
            xhr.open('GET', fullUrl);
        } else {
            xhr.open('POST', url);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        }
        
        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('响应格式错误'));
                }
            } else {
                reject(new Error(`请求失败: ${xhr.status}`));
            }
        };
        
        xhr.onerror = function() {
            reject(new Error('网络错误'));
        };
        
        if (method === 'POST') {
            const formData = new URLSearchParams(params).toString();
            xhr.send(formData);
        } else {
            xhr.send();
        }
    });
}

// 表格排序
function sortTable(table, columnIndex, type = 'string') {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        if (type === 'number') {
            return parseFloat(aValue) - parseFloat(bValue);
        } else if (type === 'date') {
            return new Date(aValue) - new Date(bValue);
        } else {
            return aValue.localeCompare(bValue);
        }
    });
    
    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

// 搜索表格
function searchTable(table, searchTerm) {
    const rows = table.querySelectorAll('tbody tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const match = text.includes(searchTerm.toLowerCase());
        row.style.display = match ? '' : 'none';
    });
}

// 分页处理
function createPagination(totalPages, currentPage, onPageChange) {
    const pagination = document.createElement('div');
    pagination.className = 'pagination';
    pagination.style.textAlign = 'center';
    pagination.style.marginTop = '20px';
    
    for (let i = 1; i <= totalPages; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `btn ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
        pageBtn.style.margin = '0 5px';
        pageBtn.textContent = i;
        pageBtn.onclick = () => onPageChange(i);
        pagination.appendChild(pageBtn);
    }
    
    return pagination;
}

// 导出表格为CSV
function exportTableToCSV(table, filename) {
    const rows = table.querySelectorAll('tr');
    let csv = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = Array.from(cols).map(col => {
            return `"${col.textContent.replace(/"/g, '""')}"`;
        });
        csv.push(rowData.join(','));
    });
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 获取商户秘钥
    const merchantSecret = getUrlParameter('merchant_secret');
    if (!merchantSecret) {
        showAlert('缺少商户秘钥参数', 'error');
        return;
    }
    
    // 设置全局变量
    window.merchantSecret = merchantSecret;
    
    // 初始化标签页
    const defaultTab = getUrlParameter('tab') || 'shop-info';
    switchTab(defaultTab);
    
    // 绑定标签页点击事件
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            switchTab(tabName);
            
            // 更新URL参数
            const url = new URL(window.location);
            url.searchParams.set('tab', tabName);
            window.history.pushState({}, '', url);
        });
    });
    
    // 绑定文件上传事件
    document.querySelectorAll('input[type="file"]').forEach(input => {
        input.addEventListener('change', function() {
            const preview = document.querySelector(this.dataset.preview);
            if (preview) {
                handleFileUpload(this, preview);
            }
        });
    });
}); 