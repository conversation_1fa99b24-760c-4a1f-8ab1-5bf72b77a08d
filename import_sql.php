<?php
/**
 * 数据库导入脚本
 * 用于覆盖导入install.sql文件
 */

// 数据库配置
$db_config = [
    'host' => 'localhost',
    'username' => 'yjsyjs',
    'password' => 'yjsyjs',
    'database' => 'yjsyjs',
    'charset' => 'utf8mb4'
];

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 连接数据库（不指定数据库名，因为需要先创建数据库）
    $pdo = new PDO(
        "mysql:host={$db_config['host']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$db_config['charset']}"
        ]
    );
    
    // 读取SQL文件
    $sql_file = 'install.sql';
    if (!file_exists($sql_file)) {
        throw new Exception("SQL文件 {$sql_file} 不存在");
    }
    
    $sql_content = file_get_contents($sql_file);
    if ($sql_content === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句
    $sql_statements = array_filter(
        array_map('trim', explode(';', $sql_content)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    
    // 执行SQL语句
    foreach ($sql_statements as $statement) {
        try {
            $pdo->exec($statement);
            $success_count++;
        } catch (PDOException $e) {
            $error_count++;
            $errors[] = [
                'statement' => substr($statement, 0, 100) . '...',
                'error' => $e->getMessage()
            ];
        }
    }
    
    // 返回结果
    $result = [
        'status' => 'success',
        'message' => "数据库导入完成",
        'data' => [
            'total_statements' => count($sql_statements),
            'success_count' => $success_count,
            'error_count' => $error_count,
            'errors' => $errors
        ]
    ];
    
    if ($error_count > 0) {
        $result['status'] = 'warning';
        $result['message'] = "数据库导入完成，但有 {$error_count} 个错误";
    }
    
} catch (Exception $e) {
    $result = [
        'status' => 'error',
        'message' => $e->getMessage(),
        'data' => null
    ];
}

// 输出JSON结果
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?> 