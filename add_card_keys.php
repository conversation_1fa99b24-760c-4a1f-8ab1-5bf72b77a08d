<?php
/**
 * API 8: 增加商品卡密
 * 所需参数: 商户秘钥, 商品ID, 卡密内容(卡密1xxxxxxx-卡密2xxxxxxx 每个卡密使用-分割)
 * 返回: 新增后的商品卡密列表
 */

header('Content-Type: application/json; charset=utf-8');
require_once 'config.php';

// 获取参数
$merchant_secret = $_GET['merchant_secret'] ?? '';
$product_id = $_GET['product_id'] ?? '';
$card_key_content = $_GET['card_key_content'] ?? '';

// 验证参数
if (empty($merchant_secret)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商户秘钥不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($product_id) || !is_numeric($product_id)) {
    echo json_encode([
        'status' => 'error',
        'message' => '商品ID不能为空且必须为数字',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($card_key_content)) {
    echo json_encode([
        'status' => 'error',
        'message' => '卡密内容不能为空',
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    $pdo = getDBConnection();
    
    // 验证商户
    $stmt = $pdo->prepare("SELECT * FROM merchants WHERE merchant_secret = ?");
    $stmt->execute([$merchant_secret]);
    $merchant = $stmt->fetch();
    
    if (!$merchant) {
        echo json_encode([
            'status' => 'error',
            'message' => '商户秘钥无效',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 验证商品是否属于该商户
    $stmt = $pdo->prepare("SELECT * FROM products WHERE product_id = ? AND merchant_id = ? AND status != 'deleted'");
    $stmt->execute([$product_id, $merchant['merchant_id']]);
    $product = $stmt->fetch();
    
    if (!$product) {
        echo json_encode([
            'status' => 'error',
            'message' => '商品不存在或无权限操作',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 分割卡密内容
    $card_keys = array_filter(array_map('trim', explode('-', $card_key_content)));
    
    if (empty($card_keys)) {
        echo json_encode([
            'status' => 'error',
            'message' => '卡密内容格式错误',
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    $inserted_count = 0;
    
    // 批量插入卡密
    $stmt = $pdo->prepare("INSERT INTO card_keys (merchant_id, product_id, product_name, card_key_content, card_key_status) VALUES (?, ?, ?, ?, 'unsold')");
    
    foreach ($card_keys as $card_key) {
        if (!empty($card_key)) {
            $stmt->execute([
                $merchant['merchant_id'],
                $product_id,
                $product['product_name'],
                $card_key
            ]);
            $inserted_count++;
        }
    }
    
    // 更新商品库存数量
    $stmt = $pdo->prepare("UPDATE products SET stock_quantity = (SELECT COUNT(*) FROM card_keys WHERE product_id = ? AND card_key_status = 'unsold') WHERE product_id = ?");
    $stmt->execute([$product_id, $product_id]);
    
    // 提交事务
    $pdo->commit();
    
    // 获取新增后的卡密列表
    $stmt = $pdo->prepare("SELECT card_key_id, merchant_id, product_id, product_name, card_key_content, card_key_status, created_at FROM card_keys WHERE product_id = ? ORDER BY created_at DESC");
    $stmt->execute([$product_id]);
    $card_key_list = $stmt->fetchAll();
    
    $result = [
        'inserted_count' => $inserted_count,
        'product_id' => $product_id,
        'product_name' => $product['product_name'],
        'card_keys' => $card_key_list
    ];
    
    echo json_encode([
        'status' => 'success',
        'message' => "成功添加 {$inserted_count} 个卡密",
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'status' => 'error',
        'message' => '添加卡密失败: ' . $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}
?> 